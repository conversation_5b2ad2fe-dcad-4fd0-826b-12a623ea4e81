package tech.tiangong.pop.req.sdk.ae

import tech.tiangong.pop.resp.sdk.aliexpress.*

/**
 * 将 AliexpressProductInfoRequest 转换为海外托管商品模型
 *
 * 此扩展函数实现了从标准 AliExpress 商品信息到海外托管专用格式的完整数据转换，
 * 包括多媒体信息、SKU 列表、商品属性、详描信息等所有必要字段的映射。
 *
 * @return AliexpressLocalServiceProductDto 海外托管商品模型
 * @throws IllegalArgumentException 当必需字段缺失时抛出异常
 */
fun AliexpressProductInfoRequest.toLocalServiceProductDto(): AliexpressLocalServiceProductDto {
    return AliexpressLocalServiceProductDto(
        // 1. 多媒体信息转换
        multimedia = convertToMultimediaDto(),

        // 2. SKU 列表转换
        productSkuList = convertToProductSkuList(),

        // 3. 包裹信息转换
        packageDto = convertToPackageDto(),

        // 4. 商品基本信息转换
        productInfo = convertToProductInfoDto(),

        // 5. 商品属性列表转换
        productPropertyList = convertToProductPropertyList(),

        // 6. 详描列表转换
        detailSourceList = convertToDetailSourceList(),

        // 7. 扩展信息转换
        productExt = convertToProductExtDto()
    )
}

/**
 * 转换多媒体信息
 * 将主图、营销图、视频等多媒体资源转换为海外托管格式
 */
fun AliexpressProductInfoRequest.convertToMultimediaDto(): MultimediaDto {
    // 处理主图列表 - 从 imageURLs 字符串解析
    val mainImageList = imageURLs?.split(",")?.map { it.trim() }?.filter { it.isNotEmpty() } ?: emptyList()

    // 处理营销图列表 - 转换格式
    val marketImageList = marketImages?.map { marketImage ->
        MarketImageDto(
            url = marketImage.url ?: "",
            imageType = when (marketImage.imageType) {
                "1" -> 1 // 长图
                "2" -> 2 // 方图
                else -> 2 // 默认方图
            }
        )
    } ?: emptyList()

    // 处理视频列表 - 转换格式
    val videoList = multimedia?.videos?.map { video ->
        VideoDto(
            mediaType = video.mediaType ?: "MAIN_IMAGE_VIDEO",
            posterUrl = video.posterUrl ?: "",
            mediaId = video.mediaId ?: 0L
        )
    }

    return MultimediaDto(
        // 海外托管默认不处理分国家主图
        mainImageList = mainImageList,
        marketImageList = marketImageList,
        videoList = videoList
    )
}

/**
 * 转换 SKU 列表
 * 将标准 SKU 信息转换为海外托管 SKU 格式，包括价格、库存、属性等
 */
fun AliexpressProductInfoRequest.convertToProductSkuList(): List<ProductSkuDto> {
    return productSKUs?.map { sku ->
        ProductSkuDto(
            // 供货价 - 必需字段
            supplyPrice = sku.skuPrice!!,

            // SKU 销售属性列表
            skuPropertyList = sku.skuProperties?.map { property ->
                SkuPropertyDto(
                    skuPropertyId = property.skuPropertyId?.toLong(),
                    propertyValueId = property.propertyValueId,
                    propertyValueDefinitionName = property.propertyValueDefinitionName,
                    skuImage = property.skuImage
                )
            },

            // SKU 编码
            skuCode = sku.skuCode,

            // SKU 状态 - 默认激活
            status = "active",

            // 包裹信息 - 重量和尺寸
            packageWeight = sku.grossWeight ?: grossWeight!!,
            packageLength = (sku.packageLength ?: packageLength!!).toString(),
            packageWidth = (sku.packageWidth ?: packageWidth!!).toString(),
            packageHeight = (sku.packageHeight ?: packageHeight!!).toString(),

            // 可售库存
            sellableQuantity = sku.ipmSkuStock?.toInt() ?: 0,

            // 分目的国供货价列表
            destCountrySupplyPriceList = sku.skuNationalDiscountPrices?.map { nationalPrice ->
                DestCountrySupplyPriceDto(
                    countryCode = nationalPrice.shiptoCountry!!,
                    moneyValue = nationalPrice.discountPrice ?: sku.skuPrice!!
                )
            }

            // 重量单位 - 默认公斤
            // 使用默认值
        )
    } ?: emptyList()
}

/**
 * 转换包裹信息
 * 将商品包装相关信息转换为海外托管格式
 */
fun AliexpressProductInfoRequest.convertToPackageDto(): PackageDto {
    return PackageDto(
        lotNum = lotNum, // 每包的数量
        productUnit = productUnit, // 产品的单位
        packageType = packageType // 打包销售标识
    )
}

/**
 * 转换商品基本信息
 * 将商品标题、分类、运费模板等基本信息转换为海外托管格式
 */
fun AliexpressProductInfoRequest.convertToProductInfoDto(): ProductInfoDto {
    // 处理多语言标题列表
    val subjectList = subjectList?.map { subject ->
        SubjectDto(
            locale = subject.locale ?: "en_US",
            value = subject.value!!
        )
    }

    return ProductInfoDto(
        subjectList = subjectList!!,
        categoryId = categoryId ?: throw IllegalArgumentException("商品分类ID不能为空"),
        postageId = freightTemplateId ?: throw IllegalArgumentException("运费模板ID不能为空"),
        locale = locale ?: "en_US", // 商品原发语种
        currencyCode = currencyCode, // 货币单位
        supportCountrySupplyPrice = nationalQuoteConfig != null // 是否支持分目的国供货价
    )
}

/**
 * 转换商品属性列表
 * 将商品属性信息转换为海外托管格式
 */
fun AliexpressProductInfoRequest.convertToProductPropertyList(): List<ProductPropertyDto> {
    return productPropertys?.map { property ->
        ProductPropertyDto(
            attrNameId = property.attrNameId?.toLong(),
            attrValueId = property.attrValueId,
            attrName = property.attrName,
            attrValue = property.attrValue,
            attrValueUnit = property.attrValueUnit,
            attrValueStart = property.attrValueStart,
            attrValueEnd = property.attrValueEnd
        )
    } ?: emptyList()
}

/**
 * 转换详描列表
 * 将多语言详情描述转换为海外托管格式
 */
fun AliexpressProductInfoRequest.convertToDetailSourceList(): List<DetailSourceDto> {
    return detailSourceList?.map { detail ->
        DetailSourceDto(
            locale = detail.locale ?: "en_US",
            mobileDetail = detail.mobileDetail,
            webDetail = detail.webDetail
        )
    } ?: listOf(
        // 如果没有详描，创建默认英文详描
        DetailSourceDto(
            locale = "en_US"
        )
    )
}

/**
 * 转换扩展信息
 * 将制造商、欧盟责任人、发货期等扩展信息转换为海外托管格式
 */
fun AliexpressProductInfoRequest.convertToProductExtDto(): ProductExtDto {
    return ProductExtDto(
        manufacturerId = manufacturerId, // 制造商 ID
        msrEuId = msrEuId?.toLongOrNull(), // 欧盟责任人 ID
        deliveryTime = deliveryTime, // 发货期
        sizeChartId = sizechartId, // 尺码模板 ID
        sizeChartIdList = sizechartIdList, // 尺码模板 ID 列表
        qualificationList = qualificationStructList?.map { qualification ->
            QualificationDto(
                type = qualification.type,
                value = qualification.value,
                key = qualification.key
            )
        } // 产品资质信息
    )
}

/**
 * 将海外托管商品查询响应转换为标准商品查询响应
 *
 * 此扩展函数实现了从海外托管商品格式到标准 AliExpress 商品查询响应的完整数据转换，
 * 这是与 toLocalServiceProductDto() 相反的转换过程，用于将海外托管商品查询结果
 * 转换为标准的商品查询响应格式，以便在系统中统一处理。
 *
 * @return AliexpressProductQueryResponse 标准商品查询响应
 * @throws IllegalArgumentException 当必需字段缺失时抛出异常
 */
fun AliexpressLocalServiceProductQueryResponse.toAliexpressProductQueryResponse(): AliexpressProductQueryResponse {
    // 如果查询不成功或商品数据为空，返回失败响应
    if (success != true || product == null) {
        return AliexpressProductQueryResponse(
            msg = "海外托管商品查询失败或商品不存在"
        )
    }

    return AliexpressProductQueryResponse(
        result = product.convertToQueryProductDetailResult()
    )
}

/**
 * 将海外托管商品模型转换为标准商品查询结果
 * 实现从海外托管格式到标准格式的完整字段映射
 */
private fun AliexpressLocalServiceProductDto.convertToQueryProductDetailResult(): QueryProductDetailResult {
    return QueryProductDetailResult(
        // 基本商品信息转换
        // 海外托管模型中没有直接的商品ID字段
        categoryId = productInfo.categoryId.toInt(),
        freightTemplateId = productInfo.postageId,
        currencyCode = productInfo.currencyCode,
        locale = productInfo.locale,

        // 包装信息转换
        lotNum = packageDto.lotNum,
        productUnit = packageDto.productUnit,
        packageType = packageDto.packageType,

        // 扩展信息转换
        manufacturerId = productExt.manufacturerId,
        msrEuId = productExt.msrEuId?.toString(),
        deliveryTime = productExt.deliveryTime,
        sizechartId = productExt.sizeChartId,
        sizechartIdList = productExt.sizeChartIdList,

        // 多媒体信息转换
        imageUrls = multimedia.mainImageList.joinToString(","),
        multimedia = convertToQueryMultimedia(),
        marketImages = convertToQueryMarketImages(),

        // 商品标题列表转换
        subjectList = convertToQuerySubjects(),

        // 详描列表转换
        detailSourceList = convertToQueryDetailSources(),

        // 商品属性列表转换
        productPropertys = convertToQueryProductProperties(),

        // SKU 列表转换
        productSkus = convertToQueryProductSKUs(),

        // 分国家报价配置转换
        nationalQuoteConfig = convertToQueryNationalQuoteConfig(),

        // 资质信息转换
        qualificationStructList = convertToQueryQualificationStructs()

        // 其他字段使用默认值或 null
    )
}

/**
 * 转换多媒体信息
 * 将海外托管多媒体格式转换为标准查询格式
 */
private fun AliexpressLocalServiceProductDto.convertToQueryMultimedia(): QueryMultimedia? {
    val videos = multimedia.videoList?.map { video ->
        QueryVideo(
            mediaId = video.mediaId,
            mediaType = video.mediaType,
            posterUrl = video.posterUrl
        )
    }

    return if (videos?.isNotEmpty() == true) {
        QueryMultimedia(videos = videos)
    } else {
        null
    }
}

/**
 * 转换营销图列表
 * 将海外托管营销图格式转换为标准查询格式
 */
private fun AliexpressLocalServiceProductDto.convertToQueryMarketImages(): List<QueryMarketImage>? {
    return multimedia.marketImageList.takeIf { it.isNotEmpty() }?.map { marketImage ->
        QueryMarketImage(
            url = marketImage.url,
            imageType = marketImage.imageType
        )
    }
}

/**
 * 转换商品标题列表
 * 将海外托管标题格式转换为标准查询格式
 */
private fun AliexpressLocalServiceProductDto.convertToQuerySubjects(): List<QuerySubject>? {
    return productInfo.subjectList.takeIf { it.isNotEmpty() }?.map { subject ->
        QuerySubject(
            locale = subject.locale,
            value = subject.value
        )
    }
}

/**
 * 转换详描列表
 * 将海外托管详描格式转换为标准查询格式
 */
private fun AliexpressLocalServiceProductDto.convertToQueryDetailSources(): List<QueryDetailSource>? {
    return detailSourceList.takeIf { it.isNotEmpty() }?.map { detail ->
        QueryDetailSource(
            locale = detail.locale,
            mobileDetail = detail.mobileDetail,
            webDetail = detail.webDetail
        )
    }
}

/**
 * 转换商品属性列表
 * 将海外托管属性格式转换为标准查询格式
 */
private fun AliexpressLocalServiceProductDto.convertToQueryProductProperties(): List<QueryProductProperty>? {
    return productPropertyList.takeIf { it.isNotEmpty() }?.map { property ->
        QueryProductProperty(
            attrName = property.attrName,
            attrNameId = property.attrNameId?.toInt(),
            attrValue = property.attrValue,
            attrValueId = property.attrValueId,
            attrValueUnit = property.attrValueUnit,
            attrValueStart = property.attrValueStart,
            attrValueEnd = property.attrValueEnd
        )
    }
}

/**
 * 转换 SKU 列表
 * 将海外托管 SKU 格式转换为标准查询格式，包括属性、价格、库存等信息
 */
private fun AliexpressLocalServiceProductDto.convertToQueryProductSKUs(): List<QueryProductSKU>? {
    return productSkuList.takeIf { it.isNotEmpty() }?.map { sku ->
        QueryProductSKU(
            // SKU 基本信息
            skuCode = sku.skuCode,
            skuPrice = sku.supplyPrice,
            ipmSkuStock = sku.sellableQuantity.toLong(),
            currencyCode = productInfo.currencyCode,

            // SKU 包装信息
            grossWeight = sku.packageWeight,
            packageLength = sku.packageLength.toIntOrNull(),
            packageWidth = sku.packageWidth.toIntOrNull(),
            packageHeight = sku.packageHeight.toIntOrNull(),

            // SKU 属性列表转换
            skuProperties = sku.skuPropertyList?.map { property ->
                QuerySKUProperty(
                    skuPropertyId = property.skuPropertyId?.toInt(),
                    propertyValueId = property.propertyValueId,
                    propertyValueDefinitionName = property.propertyValueDefinitionName,
                    skuImage = property.skuImage
                )
            },

            // 分国家促销价转换
            skuNationalDiscountPrices = sku.destCountrySupplyPriceList?.map { countryPrice ->
                QuerySKUNationalDiscountPrice(
                    shiptoCountry = countryPrice.countryCode,
                    discountPrice = countryPrice.moneyValue
                )
            }

            // 其他字段使用默认值
        )
    }
}

/**
 * 转换分国家报价配置
 * 将海外托管分国家定价信息转换为标准查询格式
 */
private fun AliexpressLocalServiceProductDto.convertToQueryNationalQuoteConfig(): QueryNationalQuoteConfig? {
    // 检查是否支持分国家供货价
    return if (productInfo.supportCountrySupplyPrice == true) {
        QueryNationalQuoteConfig(
            configurationType = "COUNTRY_SUPPLY_PRICE",
            configurationData = "海外托管分国家定价配置"
        )
    } else {
        null
    }
}

/**
 * 转换资质信息列表
 * 将海外托管资质格式转换为标准查询格式
 */
private fun AliexpressLocalServiceProductDto.convertToQueryQualificationStructs(): List<QueryQualificationStruct>? {
    return productExt.qualificationList?.takeIf { it.isNotEmpty() }?.map { qualification ->
        QueryQualificationStruct(
            key = qualification.key,
            type = qualification.type,
            value = qualification.value
        )
    }
}
